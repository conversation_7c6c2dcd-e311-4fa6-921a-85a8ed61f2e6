import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/Button'

interface ConfirmationPageProps {
  params: {
    id: string
  }
}

// Mock booking data (in production, fetch from Sanity)
const mockBooking = {
  _id: '1',
  bookingNumber: 'RS20241201ABCD',
  type: 'tour',
  status: 'confirmed',
  pricing: {
    totalPrice: 24000,
    currency: 'KES'
  },
  tourBooking: {
    tourName: 'Maasai Mara Wildlife Safari',
    date: '2024-12-15',
    time: '06:00',
    duration: 8,
    guests: {
      adults: 2,
      children: 0,
      infants: 0
    }
  },
  contact: {
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '+254712345678'
  },
  payment: {
    method: 'mpesa',
    status: 'completed',
    transactionId: 'QHX123456789',
    paidAt: '2024-12-01T10:30:00Z'
  },
  confirmedAt: '2024-12-01T10:30:00Z'
}

export const metadata: Metadata = {
  title: 'Booking Confirmed | RiftStays',
  description: 'Your booking has been confirmed successfully',
}

export default function ConfirmationPage({ params }: ConfirmationPageProps) {
  // In production, fetch booking data based on ID
  const booking = mockBooking

  if (!booking) {
    notFound()
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  const formatTime = (timeString: string) => {
    return new Date(`2000-01-01T${timeString}`).toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    })
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="mx-auto max-w-3xl px-4 sm:px-6 lg:px-8">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
            <svg className="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">Booking Confirmed!</h1>
          <p className="mt-2 text-gray-600">
            Your payment has been processed and your booking is confirmed
          </p>
        </div>

        {/* Booking Details */}
        <div className="bg-white rounded-lg shadow-sm overflow-hidden mb-8">
          <div className="px-6 py-4 bg-green-50 border-b border-green-200">
            <h2 className="text-lg font-semibold text-green-900">
              Booking #{booking.bookingNumber}
            </h2>
            <p className="text-sm text-green-700">
              Confirmed on {formatDate(booking.confirmedAt!)}
            </p>
          </div>
          
          <div className="p-6 space-y-6">
            {/* Tour Details */}
            {booking.tourBooking && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Tour Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Tour Name</label>
                    <p className="mt-1 text-sm text-gray-900">{booking.tourBooking.tourName}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Date</label>
                    <p className="mt-1 text-sm text-gray-900">{formatDate(booking.tourBooking.date)}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Time</label>
                    <p className="mt-1 text-sm text-gray-900">{formatTime(booking.tourBooking.time)}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Duration</label>
                    <p className="mt-1 text-sm text-gray-900">{booking.tourBooking.duration} hours</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Guests</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {booking.tourBooking.guests.adults} Adults
                      {booking.tourBooking.guests.children > 0 && `, ${booking.tourBooking.guests.children} Children`}
                      {booking.tourBooking.guests.infants > 0 && `, ${booking.tourBooking.guests.infants} Infants`}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Customer Information */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Name</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {booking.contact.firstName} {booking.contact.lastName}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Email</label>
                  <p className="mt-1 text-sm text-gray-900">{booking.contact.email}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Phone</label>
                  <p className="mt-1 text-sm text-gray-900">{booking.contact.phone}</p>
                </div>
              </div>
            </div>

            {/* Payment Information */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">Amount Paid</label>
                  <p className="mt-1 text-sm text-gray-900 font-semibold">
                    {formatPrice(booking.pricing.totalPrice)}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Payment Method</label>
                  <p className="mt-1 text-sm text-gray-900 capitalize">{booking.payment.method}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Transaction ID</label>
                  <p className="mt-1 text-sm text-gray-900 font-mono">{booking.payment.transactionId}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Payment Date</label>
                  <p className="mt-1 text-sm text-gray-900">
                    {formatDate(booking.payment.paidAt!)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold text-blue-900 mb-4">What's Next?</h3>
          <ul className="space-y-2 text-blue-800">
            <li className="flex items-start">
              <svg className="h-5 w-5 text-blue-600 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              You will receive a confirmation email with detailed instructions
            </li>
            <li className="flex items-start">
              <svg className="h-5 w-5 text-blue-600 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              Our team will contact you 24 hours before your tour
            </li>
            <li className="flex items-start">
              <svg className="h-5 w-5 text-blue-600 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              Please arrive at the meeting point 15 minutes early
            </li>
            <li className="flex items-start">
              <svg className="h-5 w-5 text-blue-600 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              Bring a valid ID and comfortable clothing
            </li>
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/tours">
            <Button>
              Book Another Tour
            </Button>
          </Link>

          <Link href="/">
            <Button variant="outline">
              Return to Homepage
            </Button>
          </Link>
          
          <Button 
            variant="outline"
            onClick={() => window.print()}
          >
            Print Confirmation
          </Button>
        </div>

        {/* Contact Information */}
        <div className="mt-8 text-center text-sm text-gray-600">
          <p>
            Need help? Contact us at{' '}
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
              <EMAIL>
            </a>{' '}
            or{' '}
            <a href="tel:+254700000000" className="text-blue-600 hover:underline">
              +254 700 000 000
            </a>
          </p>
        </div>
      </div>
    </div>
  )
}
